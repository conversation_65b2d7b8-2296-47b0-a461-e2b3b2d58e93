import { ASSISTANT_ROLE, USER_ROLE } from '@/config/chat';
import { guessYouWant } from '@/services/chat';
import { ErrorMessageType, mergeByCollectionId } from '@/utils';
import { Metadata } from '@douyinfe/semi-ui/lib/es/chat/interface';
import { ReactNode } from 'react';
import { Effect, Reducer } from 'umi';

export interface ChatContent {
  type: 'text' | 'image_url' | 'file_url';
  text?: string;
  image_url?: {
    url: string;
    [x: string]: any;
  };
  file_url?: {
    url: string;
    name: string;
    size: string;
    type: string;
    [x: string]: any;
  };
}

export interface CustomRenderContentProps {
  message?: ChatMessage;
  role?: Metadata;
  defaultContent?: ReactNode | ReactNode[];
  className?: string;
}

export interface ChatMessage {
  role?: string;
  name?: string;
  id?: string;
  content?: string | ChatContent[];
  contentJson?: Record<string, any>;
  parentId?: string;
  createAt?: number;
  isReConnect?: boolean;
  status?: 'loading' | 'incomplete' | 'complete' | 'error' | 'reasoning' | 'progress';
}

export interface ChatType {
  messages: ChatMessage[];
  pending: boolean;
  abortController: AbortController;
  hints: string[];
}

export type ChatInfoType = Record<string, ChatType>;

// 定义引用项接口
export interface ReferenceItem {
  type: 'image' | 'file';
  name?: string;
  url?: string;
  fileType?: string;
  fileIcon?: any;
  fileTags?: string[];
  size?: string;
  content?: string; // 文本引用内容
}

export interface ChatModelState {
  isOnline: boolean;
  isBothNotEmpty: boolean;
  chatMsg: ChatInfoType | null;
  references: ReferenceItem[];
  isExpand: boolean;
  isShowVditor: boolean;
  realTimeContent: any;
  imageRouteId: string;
  imageConfig: any;
}

export interface ChatModelType {
  namespace: 'chat';
  state: ChatModelState;
  effects: {
    fetchHints: Effect;
    clearHints: Effect;
  };
  reducers: {
    setIsExpand: Reducer<ChatModelState>;
    setIsShowVditor: Reducer<ChatModelState>;
    setRealTimeContent: Reducer<ChatModelState>;
    saveMessage: Reducer<ChatModelState>;
    chunkMessage: Reducer<ChatModelState>;
    saveFlowResponses: Reducer<ChatModelState>;
    updatePending: Reducer<ChatModelState>;
    updateAbortController: Reducer<ChatModelState>;
    deleteChatId: Reducer<ChatModelState>;
    setOnlineStatus: Reducer<ChatModelState>;
    setBothNotEmpty: Reducer<ChatModelState>;
    updateChatById: Reducer<ChatModelState>;
    addReferences: Reducer<ChatModelState>; // 添加引用
    updateReferences: Reducer<ChatModelState>; // 更新引用列表
    clearReferences: Reducer<ChatModelState>; // 清空引用
    setRichTextContent: Reducer<ChatModelState>; // 设置富文本内容
    updateHints: Reducer<ChatModelState>;
    updatePptInfo: Reducer<ChatModelState>;
    setSmarInputPlaceholder: Reducer<JSX.Element>;
    setImageRouteId: Reducer<ChatModelState>;
    setImageConfig: Reducer<ChatModelState>;
    updateImageProgress: Reducer<ChatModelState>; // 更新图片生成进度
    updateButtonParams: Reducer<ChatModelState>; // 更新按钮参数
  };
}

const ChatModel: ChatModelType = {
  namespace: 'chat',

  state: {
    isOnline: false, // 是否联网查询
    isBothNotEmpty: false, // 联网查询是否禁用
    chatMsg: {},
    references: [], // 初始化引用列表为空
    isExpand: false, // 分成两列模式
    realTimeContent: '', // 实时内容
    isShowVditor: false, // 是否显示vditor
    imageRouteId: '', //文生图tab routerID
    imageConfig: {}, // 文生图配置
  },

  effects: {
    *fetchHints({ payload }, { call, put }): Generator<any, void, any> {
      const { chatId, appCode } = payload;

      const response: any = yield call(guessYouWant, { chatId, appCode });
      yield put({
        type: 'updateHints',
        payload: {
          chatId,
          hints: response?.data?.data || [],
        },
      });
    },
    *clearHints({ payload }, { put }): Generator<any, void, any> {
      yield put({
        type: 'updateHints',
        payload: {
          chatId: payload.chatId,
          hints: [],
        },
      });
    },
  },

  reducers: {
    // 是否显示md编辑器
    setIsShowVditor(state, { payload }) {
      return {
        ...state,
        isShowVditor: payload,
      };
    },
    // 设置富文本显示内容
    setRealTimeContent(state, { payload }) {
      return {
        ...state,
        realTimeContent: payload,
      };
    },
    setIsExpand(state, { payload }) {
      return {
        ...state,
        isExpand: payload,
      };
    },
    // 设置富文本内容
    setRichTextContent(state, { payload }) {
      return {
        ...state,
        useRichText: payload.useRichText,
        richTextValue: payload.richTextValue,
      };
    },

    // 添加文件/图片引用项
    addReferences(state, { payload }) {
      return {
        ...state,
        references: payload || [], // 只取第一个引用
      };
    },

    // 更新引用列表
    updateReferences(state, { payload }) {
      return {
        ...state,
        references: payload,
      };
    },

    // 清空引用
    clearReferences(state) {
      return {
        ...state,
        references: [],
      };
    },

    // 修改 isOnline 状态
    setOnlineStatus(state, { payload }) {
      return {
        ...state,
        isOnline: payload,
      };
    },
    // 修改 isBothNotEmpty 状态
    setBothNotEmpty(state, { payload }) {
      return {
        ...state,
        isBothNotEmpty: payload,
      };
    },
    // chatId对话
    saveMessage(state, action) {
      const { appCode } = action.payload;
      Object.values(action.payload as ChatInfoType).forEach((chatItem) => {
        const lastAssistantIndex = chatItem.messages?.findLastIndex(
          (msg: ChatMessage) => msg.role === ASSISTANT_ROLE,
        );
        if (
          lastAssistantIndex > -1 &&
          chatItem.messages[lastAssistantIndex]?.status !== 'complete'
        ) {
          chatItem.hints = [];
        }
        chatItem.messages?.forEach((msg: ChatMessage) => {
          if (msg.role === ASSISTANT_ROLE) {
            // msg.content = Array.isArray(msg.content)
            //   ? msg.content.map((item) =>
            //       item.type === 'text' ? { ...item, text: preprocessLaTeX(item.text || '') } : item,
            //     )
            //   : preprocessLaTeX(msg.content || '');
            // 处理生成ppt按钮展示时机
            if (appCode === 'ppt') {
              msg.isGeneratePPT =
                !msg?.pptDetail?.taskId && (!msg.status || msg.status === 'complete');
            }
          }
        });

        const lastUserIndex = chatItem.messages?.findLastIndex(
          (msg: ChatMessage) => msg.role === USER_ROLE,
        );
        if (!action.isEdit && lastUserIndex > -1) {
          chatItem.messages[lastUserIndex].isEdit = false;
        }
      });
      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          ...action.payload,
        },
      };
    },
    // 处理flowResponses
    saveFlowResponses(state, { payload }) {
      const { flowResponses, chatId } = payload;

      const datasetSearchNode = (flowResponses || []).findLast((item: any) => item.quoteList);
      const quoteList = mergeByCollectionId(datasetSearchNode?.quoteList || []);

      // 更新消息列表，为每条消息添加 quoteList
      const updatedMessage = {
        ...state.chatMsg![chatId]?.messages.at(-1),
        quoteList,
      };

      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          [chatId]: {
            ...state.chatMsg![chatId],
            messages: [...state.chatMsg![chatId]?.messages.slice(0, -1), updatedMessage],
          },
        },
      };
    },
    // 数据流更新
    chunkMessage(state, action) {
      const lastMsg: ChatMessage = { ...state.chatMsg![action.payload.chatId].messages.at(-1) };
      if (lastMsg.role === ASSISTANT_ROLE) {
        const lastMessage: ChatMessage = {
          ...lastMsg,
          status: 'incomplete',
        };
        const isLoading = action.payload.type === 'loading';
        if (action.payload.type === 'answer_json') {
          lastMessage.contentJson = action.payload.chunk;
        } else if (action.payload.type === 'reasoning' || isLoading) {
          lastMessage.status = isLoading ? 'loading' : 'reasoning';
          lastMessage.reasoningContent = (lastMsg.reasoningContent || '') + action.payload.chunk;
        } else if (action.payload.type === 'error') {
          lastMessage.content = action.payload.chunk;
          lastMessage.status = 'error';
        } else if (action.payload.type === 'progress') {
          lastMessage.status = 'progress';
          lastMessage.progressInfo = action.payload.chunk;
        } else if (action.payload.type === 'image') {
          lastMessage.content = action.payload.chunk;
        } else {
          lastMessage.content = (lastMsg.content || '') + action.payload.chunk;
          if (location.pathname.includes(action.payload.chatId)) {
            state.realTimeContent = lastMessage.content;
          }
        }
        state.chatMsg![action.payload.chatId].messages = [
          ...state.chatMsg![action.payload.chatId].messages.slice(0, -1),
          lastMessage,
        ];
      }
      return {
        ...state,
      };
    },
    // 更新对话状态
    updatePending(state, action) {
      let chatItem: ChatInfoType = {};
      const chatId = action?.payload?.chatId || '';
      if (chatId) {
        chatItem = {
          [chatId]: {
            ...state.chatMsg![chatId],
            pending: action?.payload?.pending || false,
          },
        };
      }
      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          ...chatItem,
        },
      };
    },
    // 更新响应请求
    updateAbortController(state, action) {
      let chatItem: ChatInfoType = {};
      const chatId = action?.payload?.chatId || '';
      const appCode = action?.payload?.appCode || '';
      let lastMessage = null;
      try {
        lastMessage = state.chatMsg![chatId].messages.at(-1) as ChatMessage;
      } catch (e) {
        lastMessage = null;
      }

      const status = action?.payload?.status || '';
      if (status === 'error' && lastMessage) {
        lastMessage.status = 'error';
        lastMessage.content =
          appCode === 'report_valid'
            ? ErrorMessageType.reportValidFailError
            : ErrorMessageType.requestFailError;
        state.chatMsg![chatId].messages = [
          ...state.chatMsg![chatId].messages.slice(0, -1),
          lastMessage,
        ];
      }
      if (action?.payload?.isEdit) {
        const lastIndex = state.chatMsg![chatId].messages?.findLastIndex(
          (msg: ChatMessage) => msg.role === USER_ROLE,
        );
        state.chatMsg![chatId].messages[lastIndex].isEdit = true;
      }
      if (chatId) {
        chatItem = {
          [chatId]: {
            ...state.chatMsg![chatId],
            abortController: action?.payload?.abortController || null,
          },
        };
      }
      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          ...chatItem,
        },
      };
    },
    // 删除首次缓存chatId
    deleteChatId(state, action) {
      delete state.chatMsg![action.payload.chatId];
      return {
        ...state,
      };
    },
    // 更新单条对话内容
    updateChatById(state, action) {
      const chatId = action?.payload?.chatId || '';

      const currentChatMes = state.chatMsg![chatId].messages;

      // 找到对应的chatId findIndex((item) => item.id === action?.payload?.message?.id)
      const newChatMes = currentChatMes.map((item) => {
        if (item.id === action?.payload?.message?.id) {
          return {
            ...item,
            ...action?.payload?.message,
          };
        }
        return item;
      });
      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          [chatId]: {
            ...state.chatMsg![chatId],
            messages: newChatMes,
            pending: false,
          },
        },
      };
    },
    updateHints(state, { payload }) {
      const { chatId, hints } = payload;
      if (!state.chatMsg?.[chatId]) {
        return state;
      }

      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          [chatId]: {
            ...state.chatMsg[chatId],
            hints,
          },
        },
      };
    },
    /**
     * 更新PPT信息
     */
    updatePptInfo(state, { payload }) {
      const chatId = payload?.chatId || '';
      const currentChatMes = state.chatMsg![chatId].messages;
      const changeKey = currentChatMes.findIndex((item) => item.id === payload.resId);
      currentChatMes[changeKey] = {
        ...currentChatMes[changeKey],
        isGeneratePPT: false,
        pptDetail: payload.pptDetail,
      };
      return {
        ...state,
        [chatId]: {
          ...state.chatMsg![chatId],
          messages: currentChatMes,
        },
      };
    },
    setSmarInputPlaceholder(state, { payload }) {
      return {
        ...state,
        smarInputPlaceholder: payload.smarPlaceholder,
      };
    },
    // 修改文生图tab routerID
    setImageRouteId(state, { payload }) {
      return {
        ...state,
        imageRouteId: payload, // 接收 payload 作为新的 routeId 值
      };
    },
    // 文生图配置
    setImageConfig(state, { payload }) {
      return {
        ...state,
        imageConfig: payload,
      };
    },
    // 更新图片生成进度
    updateImageProgress(state, { payload }) {
      const { chatId, messageId, progress } = payload;
      if (!state.chatMsg?.[chatId]) return state;
      const newMessages = state.chatMsg[chatId].messages.map((msg) =>
        msg.id === messageId ? { ...msg, progress } : msg,
      );
      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          [chatId]: {
            ...state.chatMsg[chatId],
            messages: newMessages,
          },
        },
      };
    },
    // 更新对话状态
    updateButtonParams(state, { payload }) {
      let chatItem: ChatInfoType = {};
      const chatId = payload?.chatId || '';
      if (chatId) {
        chatItem = {
          [chatId]: {
            ...state.chatMsg![chatId],
            imageButtonParams: payload?.imageButtonParams || {},
          },
        };
      }
      return {
        ...state,
        chatMsg: {
          ...state.chatMsg,
          ...chatItem,
        },
      };
    },
  },
};

export default ChatModel;
