import EmptyImage from '@/assets/chat/fallbackImage.svg';
import { Text2ImgModelState } from '@/models/text2Img';
import ImgMasonry from '@/pages/Office/components/ImgMasonry';
import {
  deleteTextImageHistory,
  fetchTextImageHistory,
  ImagesType,
  TextImageMenuList,
} from '@/services/text2Img';
import { convertTextToRichInput, dispatchInUtils } from '@/utils';
import { TabPane, Tabs, Toast } from '@douyinfe/semi-ui';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { ImageItemType } from '../components/ImgMasonry';
import styles from './index.less';
interface Text2ImgProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}
const Text2Img: React.FC<Text2ImgProps> = ({ activeTab, onTabChange }) => {
  const { navList } = useSelector((state: { text2Img: Text2ImgModelState }) => state.text2Img);
  const text2ImgRef = useRef<HTMLDivElement | null>(null);
  const [images, setImages] = useState<ImageItemType[]>([]);
  const [loading, setLoading] = useState(false);
  const [containerHeight, setContainerHeight] = useState(0);
  const dispatch = useDispatch();

  // 初始化状态管理
  const [currentTabId, setCurrentTabId] = useState<string>(activeTab || '');
  const [isInitialized, setIsInitialized] = useState(false);

  // 虚拟滚动相关状态
  const [visibleImages, setVisibleImages] = useState<ImageItemType[]>([]);
  const [batchSize] = useState(20); // 每批渲染的数量
  const renderTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 滚动性能优化
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isScrolling, setIsScrolling] = useState(false);

  // 当前渲染的tab标识，用于防止tab切换时的数据混乱
  const currentRenderingTabRef = useRef<string>(activeTab || '');

  // 防抖定时器，防止频繁请求
  const fetchDebounceRef = useRef<NodeJS.Timeout | null>(null);

  // 请求状态管理，防止重复请求
  const requestingTabRef = useRef<string>('');

  // 优化的分批渲染函数，支持虚拟滚动和性能优化
  const renderImagesBatch = React.useCallback(
    (allImages: ImageItemType[], immediate = false, tabId?: string) => {
      // 设置当前渲染的tab标识
      if (tabId) {
        currentRenderingTabRef.current = tabId;
      }

      // 清除之前的定时器
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }

      // 如果没有图片，直接返回
      if (allImages.length === 0) {
        setVisibleImages([]);
        return;
      }

      // 立即渲染模式：直接渲染前20条，避免loading状态
      if (immediate) {
        const initialBatch = allImages.slice(0, batchSize);
        setVisibleImages(initialBatch);

        // 如果还有更多图片，使用 requestIdleCallback 在空闲时渲染
        if (allImages.length > batchSize) {
          const renderRemaining = (batchIndex: number) => {
            // 检查是否还是当前tab，如果已切换则停止渲染
            if (tabId && currentRenderingTabRef.current !== tabId) {
              return;
            }

            const endIndex = batchIndex * batchSize;
            const batchImages = allImages.slice(0, endIndex);

            setVisibleImages(batchImages);

            if (endIndex < allImages.length) {
              // 使用 requestIdleCallback 优化性能
              if (window.requestIdleCallback) {
                window.requestIdleCallback(
                  () => {
                    // 再次检查tab是否切换
                    if (tabId && currentRenderingTabRef.current === tabId) {
                      renderRemaining(batchIndex + 1);
                    }
                  },
                  { timeout: 100 },
                );
              } else {
                renderTimerRef.current = setTimeout(() => {
                  // 再次检查tab是否切换
                  if (tabId && currentRenderingTabRef.current === tabId) {
                    renderRemaining(batchIndex + 1);
                  }
                }, 32); // 降低频率，减少卡顿
              }
            }
          };

          // 延迟开始后续渲染，让首批渲染完成
          renderTimerRef.current = setTimeout(() => {
            // 检查tab是否还是当前的
            if (tabId && currentRenderingTabRef.current === tabId) {
              renderRemaining(2);
            }
          }, 100);
        }
        return;
      }

      // 普通分批渲染逻辑（用于删除等操作）
      setVisibleImages([]);
      const renderBatch = (batchIndex: number) => {
        // 检查是否还是当前tab
        if (tabId && currentRenderingTabRef.current !== tabId) {
          return;
        }

        const endIndex = batchIndex * batchSize;
        const batchImages = allImages.slice(0, endIndex);

        setVisibleImages(batchImages);

        if (endIndex < allImages.length) {
          renderTimerRef.current = setTimeout(() => {
            // 再次检查tab是否切换
            if (tabId && currentRenderingTabRef.current === tabId) {
              renderBatch(batchIndex + 1);
            }
          }, 32); // 降低渲染频率
        }
      };

      renderBatch(1);
    },
    [batchSize],
  );

  // useEffect(() => {
  //   dispatchInUtils({
  //     type: 'text2Img/fetchTextImageMenuList',
  //   });
  // }, []);

  // 初始化navList时的处理
  useEffect(() => {
    if (navList?.length && !isInitialized) {
      const firstTabId = String(navList[0].routeId);

      // 如果没有activeTab，设置第一个tab为默认
      if (!activeTab) {
        onTabChange(firstTabId);
        setCurrentTabId(firstTabId);
        currentRenderingTabRef.current = firstTabId;
      } else {
        // 如果有activeTab，确保currentTabId同步
        setCurrentTabId(activeTab);
        currentRenderingTabRef.current = activeTab;
      }

      const currentTabData = navList.find(
        (item) => item.routeId === Number(activeTab || firstTabId),
      );
      const richTextValue = convertTextToRichInput(currentTabData?.prompt || '');
      dispatchInUtils({
        type: 'chat/setRichTextContent',
        payload: {
          useRichText: true,
          richTextValue,
        },
      });

      setIsInitialized(true);
    }
  }, [navList?.length, isInitialized]); // 移除activeTab和onTabChange依赖，避免循环
  useEffect(() => {
    // 获取 semi-chat-inner 元素
    const element = document.querySelector('.semi-chat-inner');
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const { height: semiChatInnerHeight } = entry.contentRect;
          const windowHeight = window.innerHeight;
          const containerHeight = windowHeight - 140 - semiChatInnerHeight;
          setContainerHeight(containerHeight);
          if (text2ImgRef.current) {
            text2ImgRef.current.style.height = `${containerHeight}px`;
          }
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
    return () => {};
  }, []);

  // 根据图片的宽高比计算高度，宽度固定是194px，得到整数，如何是NaN，返回固定279高度
  const displayHeight = (item: ImagesType) => {
    const height = Math.round((item.height / item.width) * 194);
    return height && !isNaN(height) ? height : 279;
  };

  // 优化的图片数据获取函数，支持分批渲染和状态管理
  const fetchImageData = React.useCallback(
    async (routeId: string) => {
      // 防止重复请求同一个tab
      if (requestingTabRef.current === routeId) {
        console.log('防止重复请求:', routeId);
        return;
      }

      try {
        console.log('开始请求图片数据:', routeId);
        requestingTabRef.current = routeId;
        setLoading(true);
        // 立即清空可见图片，避免显示旧数据
        setVisibleImages([]);

        // 更新当前渲染的tab标识
        currentRenderingTabRef.current = routeId;

        dispatch({
          type: 'chat/setImageRouteId',
          payload: activeTab,
        });

        const res = await fetchTextImageHistory({ routeId });

        // 检查请求完成时是否还是当前tab
        if (currentRenderingTabRef.current !== routeId) {
          return;
        }

        if (res.data && res.data.length > 0) {
          const newResList = res.data.map((item) => {
            return {
              id: item.id + '',
              url: item.coverImage.url,
              width: item.coverImage.width,
              height: displayHeight(item.coverImage),
              name: item.coverImage.name,
              tags: [item.coverImage.name],
              images: item.images.map((item) => item.url),
            };
          });

          // 再次检查是否还是当前tab
          if (currentRenderingTabRef.current === routeId) {
            setImages(newResList);
            renderImagesBatch(newResList, true, routeId);
          }
        } else {
          if (currentRenderingTabRef.current === routeId) {
            setImages([]);
            setVisibleImages([]);
          }
        }
      } catch (error) {
        console.error('Failed to fetch images:', error);
        if (currentRenderingTabRef.current === routeId) {
          setImages([]);
          setVisibleImages([]);
        }
      } finally {
        if (currentRenderingTabRef.current === routeId) {
          setLoading(false);
        }
        // 清除请求状态
        if (requestingTabRef.current === routeId) {
          requestingTabRef.current = '';
        }
      }
    },
    [dispatch, renderImagesBatch], // 移除activeTab依赖，避免循环
  );

  // 初始化完成后加载首个tab的数据
  useEffect(() => {
    if (
      isInitialized &&
      activeTab &&
      !loading &&
      images.length === 0 &&
      requestingTabRef.current !== activeTab
    ) {
      fetchImageData(activeTab);
    }
  }, [isInitialized, activeTab, loading, images.length]); // 移除fetchImageData依赖

  // 监听activeTab变化，同步currentTabId并加载数据
  useEffect(() => {
    if (activeTab && isInitialized && currentTabId !== activeTab) {
      // 同步currentTabId
      setCurrentTabId(activeTab);
      currentRenderingTabRef.current = activeTab;

      // 只有当tab真正改变时才请求数据
      if (requestingTabRef.current !== activeTab) {
        fetchImageData(activeTab);
      }
    }
  }, [activeTab, isInitialized, currentTabId]); // 重新添加currentTabId依赖用于比较

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }
      if (fetchDebounceRef.current) {
        clearTimeout(fetchDebounceRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);
  const handleTabChange = React.useCallback(
    (key: string) => {
      // 防止重复切换到同一个tab
      if (currentTabId === key) {
        return;
      }

      // 清除防抖定时器
      if (fetchDebounceRef.current) {
        clearTimeout(fetchDebounceRef.current);
      }

      // 立即更新当前tab标识和渲染标识
      setCurrentTabId(key);
      currentRenderingTabRef.current = key;

      // 立即设置loading状态，防止显示旧数据
      setLoading(true);

      // 立即清空当前显示的图片，避免显示旧数据
      setVisibleImages([]);

      // 清除渲染定时器
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }

      // 通知父组件tab变化
      onTabChange(key);

      // 滚动到顶部
      text2ImgRef.current?.scrollTo(0, 0);

      // 防抖更新富文本内容，避免频繁触发
      fetchDebounceRef.current = setTimeout(() => {
        const currentTabData = navList.find((item) => item.routeId === Number(key));
        const richTextValue = convertTextToRichInput(currentTabData?.prompt || '');
        dispatchInUtils({
          type: 'chat/setRichTextContent',
          payload: {
            useRichText: true,
            richTextValue,
          },
        });
      }, 100); // 100ms防抖
    },
    [onTabChange, navList, currentTabId],
  );

  const deleteTextImage = React.useCallback(
    async (id: string) => {
      try {
        const { data } = await deleteTextImageHistory({ id: Number(id) });
        if (data) {
          // 同时更新图片列表和可见图片列表
          const updatedImages = images.filter((item) => item.id !== id);
          const updatedVisibleImages = visibleImages.filter((item) => item.id !== id);

          setImages(updatedImages);
          setVisibleImages(updatedVisibleImages);

          Toast.success('删除成功');
        }
      } catch (error) {
        console.error('Delete failed:', error);
        Toast.error('删除失败，请重试');
      }
    },
    [images, visibleImages],
  );

  // 滚动性能优化处理
  const handleScroll = React.useCallback(() => {
    if (!isScrolling) {
      setIsScrolling(true);
    }

    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
  }, [isScrolling]);

  // 添加滚动监听
  React.useEffect(() => {
    const container = text2ImgRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true });
      return () => {
        container.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [handleScroll]);

  // 渲染内容的优化函数
  const renderContent = React.useMemo(() => {
    // 只有在初始化完成后才进行tab切换的判断，避免首个tab显示loading
    if (isInitialized && currentTabId !== activeTab) {
      return (
        <div className={styles.loadingContainer}>
          <div className={styles.loadingText}>正在加载图片...</div>
        </div>
      );
    }

    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <div className={styles.loadingText}>正在加载图片...</div>
        </div>
      );
    }

    if (images.length === 0 && !loading) {
      return (
        <div className={styles.emptyMessageContainer} style={{ height: containerHeight - 100 }}>
          <img className={styles.emptyImage} src={EmptyImage} />
          <div className={styles.emptyMessageText}>暂无历史记录</div>
        </div>
      );
    }

    return (
      <div className={isScrolling ? styles.scrollingContainer : ''}>
        <ImgMasonry images={visibleImages} onDelete={deleteTextImage} />
      </div>
    );
  }, [loading, images, visibleImages, deleteTextImage, activeTab, currentTabId, isInitialized]);

  useEffect(() => {
    return () => {
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }
    };
  }, []);

  return (
    <div ref={text2ImgRef} className={styles.text2Img}>
      <Tabs type="line" activeKey={activeTab} onChange={handleTabChange} collapsible>
        {(navList || []).map((item: TextImageMenuList) => (
          <TabPane tab={item.routeValue} itemKey={String(item.routeId)} key={item.routeId}>
            <p className={styles.recentRecordTitle}>最近记录</p>
            {renderContent}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default Text2Img;
